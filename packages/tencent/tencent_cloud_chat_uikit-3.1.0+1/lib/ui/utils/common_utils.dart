import 'dart:convert';

import 'package:tencent_cloud_chat_uikit/data_services/core/models/custom_element.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/constants/history_message_constant.dart';

class TencentUtils {
  static bool isTextNotEmpty(String? text) {
    return text != null && text.isNotEmpty;
  }

  static String? checkString(String? text) {
    return (text != null && text.isEmpty) ? null : text;
  }

  static String? checkStringWithoutSpace(String? text) {
    if (text == null || text.trim().isEmpty || text.contains(' ')) {
      return null;
    }
    return text;
  }

  static String getFileType(String fileType) {
    return switch (fileType) {
      "3gp" => "video/3gpp",
      "torrent" => "application/x-bittorrent",
      "kml" => "application/vnd.google-earth.kml+xml",
      "gpx" => "application/gpx+xml",
      "asf" => "video/x-ms-asf",
      "avi" => "video/x-msvideo",
      "bin" || "class" || "exe" => "application/octet-stream",
      "bmp" => "image/bmp",
      "c" || "conf" || "cpp" || "h" || "prop" || "rc" || "sh" || "txt" || "log" || "java" || "xml" => "text/plain",
      "doc" => "application/msword",
      "docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "xls" || "csv" => "application/vnd.ms-excel",
      "xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "gif" => "image/gif",
      "gtar" => "application/x-gtar",
      "gz" => "application/x-gzip",
      "htm" || "html" => "text/html",
      "jar" => "application/java-archive",
      "jpeg" || "jpg" => "image/jpeg",
      "js" => "application/x-javascript",
      "m3u" => "audio/x-mpegurl",
      "m4a" || "m4b" || "m4p" => "audio/mp4a-latm",
      "m4u" => "video/vnd.mpegurl",
      "m4v" => "video/x-m4v",
      "mov" => "video/quicktime",
      "mp2" || "mp3" => "audio/x-mpeg",
      "mp4" || "mpg4" => "video/mp4",
      "mpc" => "application/vnd.mpohun.certificate",
      "mpe" || "mpeg" || "mpg" => "video/mpeg",
      "mpga" => "audio/mpeg",
      "msg" => "application/vnd.ms-outlook",
      "ogg" => "audio/ogg",
      "pdf" => "application/pdf",
      "png" => "image/png",
      "pps" || "ppt" => "application/vnd.ms-powerpoint",
      "pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      "rmvb" => "audio/x-pn-realaudio",
      "rtf" => "application/rtf",
      "tar" => "application/x-tar",
      "tgz" => "application/x-compressed",
      "wav" => "audio/x-wav",
      "wma" => "audio/x-ms-wma",
      "wmv" => "audio/x-ms-wmv",
      "wps" => "application/vnd.ms-works",
      "z" => "application/x-compress",
      "zip" => "application/x-zip-compressed",
      _ => "*/*",
    };
  }

  static V2TimMessage fromCustomMediaMessage(
    V2TimMessage message,
    MediaItem mediaItem,
  ) {
    final isVideo = mediaItem.type == MediaType.video;
    return V2TimMessage(
      msgID: message.msgID,
      timestamp: message.timestamp,
      progress: message.progress,
      sender: message.sender,
      nickName: message.nickName,
      friendRemark: message.friendRemark,
      faceUrl: message.faceUrl,
      nameCard: message.nameCard,
      groupID: message.groupID,
      userID: message.userID,
      elemType: isVideo ? MessageElemType.V2TIM_ELEM_TYPE_VIDEO : MessageElemType.V2TIM_ELEM_TYPE_IMAGE,
      videoElem: isVideo
          ? V2TimVideoElem(
              videoPath: mediaItem.url,
              localVideoUrl: mediaItem.localPath,
              duration: mediaItem.duration,
              snapshotPath: mediaItem.thumbnailPath,
              snapshotUrl: mediaItem.thumbnailUrl,
            )
          : null,
      imageElem: !isVideo
          ? V2TimImageElem(
              imageList: [
                V2TimImage(
                  type: V2TimImageTypesEnum.original.index,
                  url: mediaItem.url,
                  localUrl: mediaItem.localPath,
                )
              ],
            )
          : null,
      cloudCustomData: message.cloudCustomData,
      isSelf: message.isSelf,
      isRead: message.isRead,
      isPeerRead: message.isPeerRead,
      priority: message.priority,
      offlinePushInfo: message.offlinePushInfo,
      groupAtUserList: message.groupAtUserList,
      seq: message.seq,
      random: message.random,
      isExcludedFromUnreadCount: message.isExcludedFromUnreadCount,
      isExcludedFromLastMessage: message.isExcludedFromLastMessage,
      isSupportMessageExtension: message.isSupportMessageExtension,
      messageFromWeb: message.messageFromWeb,
      id: message.id,
      needReadReceipt: message.needReadReceipt,
      customElem: message.customElem,
      textElem: message.textElem,
      soundElem: message.soundElem,
      fileElem: message.fileElem,
      locationElem: message.locationElem,
      faceElem: message.faceElem,
      groupTipsElem: message.groupTipsElem,
      mergerElem: message.mergerElem,
      localCustomData: message.localCustomData,
      localCustomInt: message.localCustomInt,
    );
  }
}

extension ConversationExtension on String {
  bool get hasSquareBrackets => startsWith('[') && endsWith(']');
  String get processEmojiString {
    final RegExp emojiPattern = RegExp(r'\[([A-Za-z0-9_]+)\]');
    return replaceAllMapped(emojiPattern, (match) {
      final emojiName = match.group(1);
      return '[TUIEmoji_$emojiName]';
    });
  }

  String get toBase64 => hasSquareBrackets ? processEmojiString : base64.encode(utf8.encode(this));
  String get fromBase64 {
    try {
      if (isBase64) {
        return utf8.decode(base64.decode(this));
      }
      return this;
    } catch (e) {
      return this;
    }
  }

  bool get isBase64 {
    try {
      // Check if the input contains only valid Base64 characters
      final base64RegExp = RegExp(r'^[A-Za-z0-9+/]*={0,2}$');
      if (!base64RegExp.hasMatch(this)) {
        return false;
      }
      // Try decoding the input
      base64.decode(this);
      return true; // If decoding succeeds, it is valid Base64
    } catch (e) {
      return false; // If decoding fails, it's not valid Base64
    }
  }

  bool get isBase64Image {
    // Check if the string starts with a valid base64 image prefix
    final RegExp base64Pattern = RegExp(r'^data:image\/(png|jpeg|jpg|gif|webp);base64,');

    if (!base64Pattern.hasMatch(this)) return false;

    try {
      // Extract the base64 part and decode it
      final String base64Data = split(',').last;
      base64Decode(base64Data);
      return true;
    } catch (e) {
      return false;
    }
  }

  String get cleanBase64 {
    final RegExp base64Pattern = RegExp(r'data:image\/[a-zA-Z]+;base64,');
    return replaceFirst(base64Pattern, '');
  }
}
