import 'dart:math';

import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_all_info_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/logic/f_trade_list_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/subviews/subviews.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:easy_localization/easy_localization.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/widgets/pagination/common_refresher.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/shared/widgets/sort_header.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class FTradeListScreen extends StatefulWidget {
  final bool showInHomePage;
  final bool? showInCard;
  const FTradeListScreen({super.key, required this.showInHomePage, this.showInCard});

  static final int _selectedTabIdx = 0;
  static bool _sortByAll = true;
  static SortType? _sortByPrice;
  static SortType? _sortByChange;

  @override
  State<FTradeListScreen> createState() => _FTradeListScreenState();
}

class _FTradeListScreenState extends State<FTradeListScreen> {
  final RefreshController _refreshController = RefreshController();
  int selectedTabIdx = 0;
  int currentPageNumber = 1;
  late bool sortByAll;
  SortType? sortByPrice;
  SortType? sortByChange;

  @override
  void initState() {
    super.initState();

    selectedTabIdx = FTradeListScreen._selectedTabIdx;
    sortByAll = FTradeListScreen._sortByAll;
    sortByPrice = FTradeListScreen._sortByPrice;
    sortByChange = FTradeListScreen._sortByChange;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      currentPageNumber = 1;
      context.read<FTradeListCubit>().fetchData(
            fExchangeIdx: selectedTabIdx,
            loadCache: true,
            loadMore: false,
            queryParameters: _makeQueryParameters(pageNumber: currentPageNumber),
          );
      context.read<FTradeListCubit>().startPolling();
    });
  }

  @override
  Widget build(BuildContext context) {
    // 首页并且加背景 GP home screen
    // 首页只加载6条数据
    if (widget.showInHomePage && widget.showInCard != true) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          FTradeListTabbar(selectedTabIdx: selectedTabIdx, onTap: _pressListTabbarBtn),
          12.verticalSpace,
          _buildLimitedTableContainer(5),
        ],
      );
    }
    // 首页并且不加背景 Style A home screen
    if (widget.showInHomePage && widget.showInCard == true) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          FTradeListTabbar(selectedTabIdx: selectedTabIdx, onTap: _pressListTabbarBtn),
          12.verticalSpace,
          Column(
            children: [
              _tableHeader(),
              _tableContent(5),
            ],
          ),
        ],
      );
    }
    // 行情内
    return Padding(
      padding: EdgeInsets.all(16.gw),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          FTradeListTabbar(selectedTabIdx: selectedTabIdx, onTap: _pressListTabbarBtn),
          12.verticalSpace,
          _buildUnlimitedTableContainer(),
        ],
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _refreshController.dispose();
  }

  /*
  ============================================================================================================================
  Actions
  ============================================================================================================================
  */

  /// 顶部不同市场的按钮点击
  void _pressListTabbarBtn(int selectedTabbarBtnIdx) {
    setState(() {
      selectedTabIdx = selectedTabbarBtnIdx;
    });
    currentPageNumber = 1;
    context.read<FTradeListCubit>().fetchData(
          fExchangeIdx: selectedTabIdx,
          loadCache: true,
          loadMore: false,
          queryParameters: _makeQueryParameters(pageNumber: currentPageNumber),
        );
    context.read<FTradeListCubit>().resetPolling();
  }

  void _onRefresh() async {
    currentPageNumber = 1;
    await context.read<FTradeListCubit>().fetchData(
          fExchangeIdx: selectedTabIdx,
          loadCache: false,
          loadMore: false,
          queryParameters: _makeQueryParameters(pageNumber: currentPageNumber),
        );
    _refreshController.refreshCompleted();
  }

  void _pressListCell(FTradeListItemModel data) async {
    context.verifyAuth(() async {
      context.read<FTradeListCubit>().stopPolling();
      await Navigator.pushNamed(
        context,
        routeFTradeAllInfo,
        arguments: (selectedTabIdx, FTradeAllInfoTitlesType.quotation, data),
      );
      if (mounted) {
        context.read<FTradeListCubit>().resetPolling();
      }
    });
  }

  void _onLoadmore() async {
    currentPageNumber = currentPageNumber + 1;
    final state = context.read<FTradeListCubit>().state;
    if (state.items.length >= state.totalNum) {
      _refreshController.loadNoData();
    } else {
      await context.read<FTradeListCubit>().fetchData(
            fExchangeIdx: selectedTabIdx,
            loadCache: false,
            loadMore: true,
            queryParameters: _makeQueryParameters(pageNumber: currentPageNumber),
          );
      _refreshController.loadComplete();
    }
  }

  /// type => 1-price 2-change
  void handleSortByChange(SortType? sortType, int type) {
    SortType? getNextSortType(SortType? currentSort) {
      if (currentSort == null) return SortType.ASC;
      if (currentSort == SortType.ASC) return SortType.DESC;
      if (currentSort == SortType.DESC) return null;
      return null;
    }

    final nextSortType = getNextSortType(sortType);

    sortByPrice = null;
    FTradeListScreen._sortByPrice = null;
    sortByChange = null;
    FTradeListScreen._sortByChange = null;

    if (type == 1) {
      sortByPrice = nextSortType;
      FTradeListScreen._sortByPrice = nextSortType;
    } else if (type == 2) {
      sortByChange = nextSortType;
      FTradeListScreen._sortByChange = nextSortType;
    }
    setState(() {});
    currentPageNumber = 1;
    context.read<FTradeListCubit>().fetchData(
          fExchangeIdx: selectedTabIdx,
          loadCache: true,
          loadMore: false,
          queryParameters: _makeQueryParameters(pageNumber: currentPageNumber),
        );
  }

  Map<String, dynamic> _makeQueryParameters({required int pageNumber}) {
    final marketType = FTradeMarketType.fromIndex(selectedTabIdx).stringIdForCloud();
    var result = {
      'marketType': marketType,
      'field': sortByKeyToParameters().$1,
      'order': sortByKeyToParameters().$2,
      'pageNumber': pageNumber,
      'pageSize': 30,
    };
    if (sortByAll == false) {
      result['isSupportNight'] = true;
    }
    return result;
  }

  // field 排序关键字 gain=>涨跌额 latestPrice=>最新价格 name=>名称排序
  // order 排序方式 DESC=>降序 ASC=>升序
  (String, String) sortByKeyToParameters() {
    if (sortByPrice != null) {
      return ("latestPrice", sortByPrice!.toParameters);
    }
    if (sortByChange != null) {
      return ("gain", sortByChange!.toParameters);
    }
    return ("volume", "DESC");
  }

  /*
  ============================================================================================================================
  TableContainerSubView
  ============================================================================================================================
  */

  Widget _buildUnlimitedTableContainer() {
    final bottomHeight = MediaQuery.of(context).padding.bottom;
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(bottom: bottomHeight),
        child: Container(
          decoration: BoxDecoration(
            color: context.theme.cardColor,
            borderRadius: BorderRadius.circular(8.gr),
            boxShadow: [
              BoxShadow(
                color: context.theme.shadowColor,
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(10.gr),
            child: Column(
              children: [
                _tableHeader(),
                8.verticalSpace,
                Flexible(
                  child: _tableContent(0),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLimitedTableContainer(int limit) {
    final bottomHeight = MediaQuery.of(context).padding.bottom;
    return Padding(
      padding: EdgeInsets.only(bottom: bottomHeight),
      child: Container(
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: BorderRadius.circular(8.gr),
          boxShadow: [
            BoxShadow(
              color: context.theme.shadowColor,
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(10.gr),
          child: Column(
            children: [
              _tableHeader(),
              8.verticalSpace,
              _tableContent(limit),
            ],
          ),
        ),
      ),
    );
  }

  /// 0 表示没有限制
  Widget _tableContent(int limit) {
    return BlocBuilder<FTradeListCubit, FTradeListState>(builder: (context, state) {
      final dataStatus = state.dataStatus;
      final items = state.items;

      if (dataStatus == DataStatus.loading) {
        return _loadingView();
      }

      if (items.isEmpty) {
        return _emptyView();
      }

      if (limit != 0) {
        return ListView.builder(
          physics: NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          itemCount: min(limit, items.length),
          itemBuilder: (context, index) => FTradeListCell(data: items[index], onTap: _pressListCell),
        );
      }

      return CommonRefresher(
        controller: _refreshController,
        enablePullDown: true,
        enablePullUp: true,
        onRefresh: _onRefresh,
        onLoading: _onLoadmore,
        bgColor: Colors.transparent,
        child: ListView.builder(
          physics: const AlwaysScrollableScrollPhysics(),
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          itemCount: items.length,
          itemBuilder: (context, index) => FTradeListCell(data: items[index], onTap: _pressListCell),
        ),
      );
    });
  }

  Widget _tableHeader() => Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 3,
            child: Row(children: [
              _FTradeListSwitch(
                isTradeSelected: sortByAll,
                onTabSelected: (selectTab) {
                  setState(() {
                    sortByAll = selectTab;
                    FTradeListScreen._sortByAll = selectTab;
                  });
                  currentPageNumber = 1;
                  context.read<FTradeListCubit>().fetchData(
                        fExchangeIdx: selectedTabIdx,
                        loadCache: true,
                        loadMore: false,
                        queryParameters: _makeQueryParameters(pageNumber: currentPageNumber),
                      );
                },
              ),
              Spacer()
            ]),
          ),
          Expanded(
            flex: context.locale.languageCode == 'en' ? 5 : 3,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SortHeader(
                  title: 'latestPrice'.tr(),
                  sortType: sortByPrice,
                  onTap: () => handleSortByChange(sortByPrice, 1),
                  textStyle: FontPalette.medium14.copyWith(
                    color: context.colorTheme.textPrimary,
                  ),
                ),
                SortHeader(
                  title: 'rise_fall'.tr(),
                  sortType: sortByChange,
                  onTap: () => handleSortByChange(sortByChange, 2),
                  textStyle: FontPalette.medium14.copyWith(
                    color: context.colorTheme.textPrimary,
                  ),
                ),
              ],
            ),
          ),
        ],
      );

  Widget _loadingView() => Column(
        children: List.generate(
          widget.showInHomePage ? 4 : 6,
          (_) => Padding(
            padding: EdgeInsets.only(bottom: 8.gh, top: 8.gh),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.gr),
              child: ShimmerWidget(
                height: 45.gh,
                width: double.infinity,
              ),
            ),
          ),
        ),
      );

  Widget _emptyView() {
    return Center(
      child: Text(
        'no_data_available'.tr(),
        style: FontPalette.medium14.copyWith(
          color: context.colorTheme.textRegular,
        ),
      ),
    );
  }
}

class _FTradeListSwitch extends StatelessWidget {
  final bool isTradeSelected;
  final Function(bool) onTabSelected;

  const _FTradeListSwitch({
    required this.isTradeSelected,
    required this.onTabSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 24.gh,
      width: 100.gw,
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        border: Border.all(color: context.theme.primaryColor),
        borderRadius: BorderRadius.circular(5.0),
      ),
      child: Row(
        children: [
          _buildTab(
            context: context,
            text: 'all'.tr(),
            isSelected: isTradeSelected,
            onTap: () => onTabSelected(true),
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(5.0),
              bottomRight: Radius.circular(5.0),
            ),
          ),
          _buildTab(
            context: context,
            text: 'night_market'.tr(),
            isSelected: !isTradeSelected,
            onTap: () => onTabSelected(false),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(5.0),
              bottomLeft: Radius.circular(5.0),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTab({
    required BuildContext context,
    required String text,
    required bool isSelected,
    required VoidCallback onTap,
    BorderRadius? borderRadius,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: isSelected ? context.theme.primaryColor : Colors.transparent,
            borderRadius: borderRadius,
          ),
          child: Text(
            text,
            style: isSelected ? context.textTheme.primary.copyWith(color: Colors.white) : context.textTheme.primary,
          ),
        ),
      ),
    );
  }
}
