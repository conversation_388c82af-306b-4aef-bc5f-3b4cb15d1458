import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';

class BuildActionButton extends StatelessWidget {
  const BuildActionButton({
    super.key,
    required this.label,
    required this.icon,
    this.onTap,
  });

  final String label;
  final String icon;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: onTap,
          icon: IconHelper.loadAsset(
            icon,
            color: context.theme.primaryColor,
          ),
        ),
        SizedBox(
          width: 80.gw,
          child: Text(
            label,
            style: FontPalette.medium11.copyWith(
              color: context.colorTheme.textPrimary,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
          ),
        ),
      ],
    );
  }
}
